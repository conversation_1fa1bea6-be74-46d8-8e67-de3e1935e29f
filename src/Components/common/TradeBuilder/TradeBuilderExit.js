import { useState, useEffect, useRef } from "react";
import { Col, Row } from "react-bootstrap";
import { DropArrowIcon, DropArrowUpIcon, PlusIcon, SolidInfoIcon, MinusIcon } from "@/assets/svgIcons/SvgIcon";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { post, get } from "@/utils/apiUtils";
import CommonButton from "@/Components/UI/CommonButton";
import ConfigScopeModal from "./ConfigScopeModal";

export default function TradeBuilderExit({
  formKey,
  formData,
  index,
  setFormData,
  transactionFields,
  tradeFields,
  portfolioFields,
  updateDatabasePayload,
  tradePublishStatus = "draft",
  tradeId,
  onDeleteForm,
  entryForms,
  onSaveStatusChange
}) {
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;
  const [linkedEntry, setLinkedEntry] = useState(null);
  const [inputValues, setInputValues] = useState({});
  const [lockedFields, setLockedFields] = useState({});
  const [touchedFields, setTouchedFields] = useState({});
  const [projectionValues, setProjectionValues] = useState({});
  const [outcomeValues, setOutcomeValues] = useState({});
  const [blurredFields, setBlurredFields] = useState({});
  const [isDimentionModal, setIsDimentionModal] = useState(false);
  const [exitIsOpen, setExitIsOpen] = useState(false);
  const [height, setHeight] = useState("0px");
  const contentRef = useRef(null);
  const [activeSection, setActiveSection] = useState("overview");
  const [inputMode, setInputMode] = useState({
    transaction_risk_percentage: true,
    transaction_quantity_purchased: false,
  });
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingToggleField, setPendingToggleField] = useState(null);
  const [notesData, setNotesData] = useState("");
  const [lastSavedNote, setLastSavedNote] = useState("");
  const [updatedCalculatedFields, setUpdatedCalculatedFields] = useState({});
  const [rawInputs, setRawInputs] = useState({});
  const [publishStatus, setPublishStatus] = useState("draft");
  const fileInputRef = useRef(null);
  const debounceRef = useRef(null);
  const notesDebounceRef = useRef(null);
  const hasAutoCalculated = useRef(false);

  const exitOverviewList = Array.isArray(formData[formKey]?.overview) ? formData[formKey].overview : [];
  const exitProjectionList = Array.isArray(formData[formKey]?.projection) ? formData[formKey].projection : [];
  const exitOutcomeList = Array.isArray(formData[formKey]?.outcome) ? formData[formKey].outcome : [];
  const formulaModeInputs = formData[formKey]?.formulaModeInputs || {};

  const openDimentionModal = () => setIsDimentionModal(true);
  const closeDimentionModal = () => setIsDimentionModal(false);

  const toggleExitCollapse = () => {
    setExitIsOpen((prev) => !prev);
  };

  useEffect(() => {
    if (exitIsOpen) {
      setHeight("auto");
    } else {
      setHeight("0px");
    }
  }, [exitIsOpen]);

  const formatValue = (value, datatype, isBlurred) => {
    const lowerType = datatype?.toLowerCase() || "";
    const isEmpty = value === null || value === undefined || value === "";

    if (isEmpty) return "";

    const num = parseFloat(value);
    const validNum = !isNaN(num) ? num : 0;

    if (lowerType.includes("date")) {
      const date = new Date(value);
      return !isNaN(date.getTime()) ? date.toISOString().slice(0, 10) : value;
    }

    if (lowerType.includes("time")) {
      const date = new Date(value);
      return !isNaN(date.getTime()) ? date.toISOString().slice(11, 19) : value;
    }

    if (!lowerType || !isBlurred) return value;

    const formatClean = (n) => Number(n).toString();

    if (lowerType.includes("percentage")) return `${formatClean(validNum)}%`;
    if (
      lowerType.includes("currency") ||
      lowerType.includes("usd") ||
      lowerType.includes("dollar") ||
      lowerType.includes("$")
    ) return `$${formatClean(validNum)}`;
    if (lowerType.includes("number")) return formatClean(validNum);

    return formatClean(validNum);
  };

  useEffect(() => {
    const protectedFields = ["transaction_quantity_purchased", "transaction_risk_percentage"];

    const initialBlurredFields = {};
    const initialInputValues = {};
    const initialLockedFields = {};
    const initialTouchedFields = {};
    const initialProjectionValues = {};
    const initialOutcomeValues = {};
    const initialInputMode = { ...inputMode };

    exitOverviewList.forEach((item) => {
      initialBlurredFields[item.input] = true;
      initialInputValues[item.input] = item?.portfolioValue ? formatValue(item.portfolioValue, item.datatype, true) : "";
      initialLockedFields[item.input] = false;
      initialTouchedFields[item.input] = false;
      if (!protectedFields.includes(item.input)) {
        initialInputMode[item.input] = formulaModeInputs[item.input] === true ? false : (item.is_editable || false);
      }
    });

    exitProjectionList.forEach((item) => {
      initialBlurredFields[item.input] = true;
      initialProjectionValues[item.input] = item?.portfolioValue ? formatValue(item.portfolioValue, item.datatype, true) : "";
      initialLockedFields[item.input] = false;
      initialTouchedFields[item.input] = false;
      if (!protectedFields.includes(item.input)) {
        initialInputMode[item.input] = formulaModeInputs[item.input] === true ? false : (item.is_editable || false);
      }
    });

    exitOutcomeList.forEach((item) => {
      initialBlurredFields[item.input] = true;
      initialOutcomeValues[item.input] = item?.portfolioValue ? formatValue(item.portfolioValue, item.datatype, true) : "";
      initialLockedFields[item.input] = false;
      initialTouchedFields[item.input] = false;
      if (!protectedFields.includes(item.input)) {
        initialInputMode[item.input] = formulaModeInputs[item.input] === true ? false : (item.is_editable || false);
      }
    });

    if (protectedFields.includes("transaction_risk_percentage") && protectedFields.includes("transaction_quantity_purchased")) {
      if (formulaModeInputs.transaction_risk_percentage === true) {
        initialInputMode.transaction_risk_percentage = false;
        initialInputMode.transaction_quantity_purchased = true;
      } else if (formulaModeInputs.transaction_quantity_purchased === true) {
        initialInputMode.transaction_quantity_purchased = false;
        initialInputMode.transaction_risk_percentage = true;
      } else {
        initialInputMode.transaction_risk_percentage = true;
        initialInputMode.transaction_quantity_purchased = false;
      }
    }

    setBlurredFields(initialBlurredFields);
    setInputValues(initialInputValues);
    setLockedFields(initialLockedFields);
    setTouchedFields(initialTouchedFields);
    setProjectionValues(initialProjectionValues);
    setOutcomeValues(initialOutcomeValues);
    setInputMode(initialInputMode);

    const notesItem = exitOverviewList.find((item) => item.input === "transaction_comments");
    if (notesItem?.portfolioValue) {
      setNotesData(notesItem.portfolioValue);
      setLastSavedNote(notesItem.portfolioValue);
    }
  }, [formKey, exitOverviewList, exitProjectionList, exitOutcomeList, formatValue]);


  useEffect(() => {
    if (hasAutoCalculated.current || !formKey || formKey === "entry_undefined") return;
  
    const doInitialAutoCalculation = async () => {
      const overview = formData[formKey]?.overview || [];
      const projection = formData[formKey]?.projection || [];
      const outcome = formData[formKey]?.outcome || [];
      const formulaInputs = formData[formKey]?.formulaModeInputs || {};
  
      const allInputs = {
        ...Object.fromEntries(overview.map((f) => [f.input, f.portfolioValue])),
        ...Object.fromEntries(projection.map((f) => [f.input, f.portfolioValue])),
        ...Object.fromEntries(outcome.map((f) => [f.input, f.portfolioValue])),
      };
  
      const formulaModeMap = {};
      Object.entries(formulaInputs).forEach(([key, isFormula]) => {
        formulaModeMap[key] = isFormula === true;
      });
  
      const changedField = Object.keys(formulaInputs).find(
        (key) => formulaInputs[key] === false && !!allInputs[key]
      );
      if (!changedField) return;
  
      try {
        const response = await post("/trade/calculate", {
          inputs: allInputs,
          locked: {},
          changedField,
          formulaModeInputs: formulaModeMap,
        });
  
        const updated = {};
        
        Object.entries(response.calculated || {}).forEach(([key, value]) => {
          updated[key.toLowerCase()] = value;
        });
  
        const updateSection = (section) =>
          section.map((item) =>
            updated[item.input] !== undefined
              ? { ...item, portfolioValue: updated[item.input] }
              : item
          );
  
          console.log(updateSection(overview));
  
        setFormData((prev) => ({
          ...prev,
          [formKey]: {
            ...prev[formKey],
            overview: updateSection(overview),
            projection: updateSection(projection),
            outcome: updateSection(outcome),
          },
        }));
  
        setInputValues((prev) => ({ ...prev, ...updated }));
        setProjectionValues((prev) => ({ ...prev, ...updated }));
        setOutcomeValues((prev) => ({ ...prev, ...updated }));
  
        setRawInputs((prev) => {
          const copy = { ...prev };
          Object.keys(updated).forEach((key) => {
            if (copy[key]) delete copy[key];
          });
          return copy;
        });
  
        hasAutoCalculated.current = true;
      } catch (err) {
        console.error("Auto-calc error:", err.response?.data || err.message);
      }
    };
  
    doInitialAutoCalculation();
  }, [formKey, formData]);

  useEffect(() => {
    if(formKey == 'exit_undefined') {
      return;
    }
    const fetchFormStatus = async () => {
      try {
        const response = await get(`/trade/form-status/${formKey}`);
        console.log(`Form status response for formKey ${formKey}:`, response);
        setPublishStatus(response.is_published ? "published" : "draft");
        if (response.is_published) {
          setLockedFields((prev) => {
            const newLocks = { ...prev };
            [...exitOverviewList, ...exitProjectionList, ...exitOutcomeList].forEach((item) => {
              newLocks[item.input] = true;
            });
            return newLocks;
          });
        }
      } catch (error) {
        console.error(`Error fetching form status for formKey ${formKey}:`, error.response?.data || error.message);
      }
    };
    if (typeof formKey === 'string' && !isNaN(parseInt(formKey))) {
      fetchFormStatus();
    }
  }, [formKey]);

  useEffect(() => {
    if (publishStatus === "published" && tradePublishStatus === "published") {
      createSnapshot();
    }
  }, [publishStatus, tradePublishStatus]);

  const toggleLock = (field) => {
    if (publishStatus === "published") return;
    setLockedFields((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const confirmToggleInputMode = (field) => {
    if (publishStatus === "published") return;
    const isSpecial =
      field === "transaction_risk_percentage" ||
      field === "transaction_quantity_purchased";

    if (!isSpecial) {
      toggleInputMode(field);
    } else {
      setPendingToggleField(field);
      setShowConfirmModal(true);
    }
  };

  const handleConfirmToggle = () => {
    if (publishStatus === "published") return;
    if (pendingToggleField) {
      toggleInputMode(pendingToggleField);
      setPendingToggleField(null);
      setShowConfirmModal(false);
    }
  };

  const handleCancelToggle = () => {
    setPendingToggleField(null);
    setShowConfirmModal(false);
  };

  const toggleInputMode = (field) => {
    setInputMode((prev) => {
      const isCurrentlyManual = prev[field];
      const newValue = !isCurrentlyManual;

      const updated = {
        ...prev,
        [field]: newValue,
      };

      if (field === "transaction_risk_percentage") {
        updated["transaction_quantity_purchased"] = !newValue;
      } else if (field === "transaction_quantity_purchased") {
        updated["transaction_risk_percentage"] = !newValue;
      }

      return updated;
    });
  };

  const handleBlur = (field) => {
    setBlurredFields((prev) => ({
      ...prev,
      [field]: true,
    }));
  };

  const handleFocus = (field) => {
    setBlurredFields((prev) => ({
      ...prev,
      [field]: false,
    }));
  };

  const saveNotes = async (notes) => {
    if (publishStatus === "published") return;
    if (onSaveStatusChange) {
      onSaveStatusChange("loading");
    }
    try {
      const response = await post("/trade/save-section", {
        formKey,
        section: "overview",
        data: [{ input: "transaction_comments", value: notes, isFormula: false }],
      });
      console.log("Save notes response:", response);
      setLastSavedNote(notes);
      updateDatabasePayload(formKey, {
        section: "overview",
        data: { TRANSACTION_COMMENTS: notes },
      });
      if (onSaveStatusChange) {
        onSaveStatusChange("success");
      }
    } catch (error) {
      console.error("Error saving notes:", error.response?.data || error.message);
      if (onSaveStatusChange) {
        onSaveStatusChange("error");
      }
    }
  };

  const handleNotesChange = (e) => {
    if (publishStatus === "published") return;
    const input = e.target.value;
    const allowedCharactersRegex = /^[a-zA-Z0-9\s.,:;!?'"()\-\n\r]*$/;

    if (allowedCharactersRegex.test(input)) {
      setNotesData(input);
      clearTimeout(notesDebounceRef.current);
      notesDebounceRef.current = setTimeout(() => {
        saveNotes(input);
      }, 500);
    }
  };

  const handleAddFields = (newFields) => {
    if (publishStatus === "published") return;
    const transformList = (list) =>
      list.map(
        ({
          field_name: title,
          summary: tooltip,
          database_field: input,
          account_field,
          portfolioValue,
          is_editable,
          datatype,
          metric_dimension,
          expected_values,
          has_formula,
        }) => ({
          title,
          tooltip,
          input,
          account_field,
          portfolioValue,
          ...(is_editable !== undefined && { is_editable }),
          datatype,
          metric_dimension,
          show_icon: true,
          expected_values: Array.isArray(expected_values) ? expected_values : [],
          has_formula,
        })
      );

    const transformedNewFields = transformList(newFields);

    setFormData((prevFormData) => {
      const currentList = Array.isArray(prevFormData[formKey]?.[activeSection])
        ? prevFormData[formKey][activeSection]
        : [];
      const existingInputs = new Set(currentList.map((item) => item.input || item.database_field));
      const uniqueFields = transformedNewFields.filter((field) => !existingInputs.has(field.input));

      return {
        ...prevFormData,
        [formKey]: {
          ...prevFormData[formKey],
          [activeSection]: [...currentList, ...uniqueFields],
        },
      };
    });

    newFields.forEach(({ database_field, is_editable }) => {
      setInputMode((prev) => ({
        ...prev,
        [database_field]: is_editable || false,
      }));
    });

    if (activeSection === "overview") {
      setInputValues((prev) => {
        const newValues = { ...prev };
        transformedNewFields.forEach((field) => {
          const fieldKey = field.input.toUpperCase();
          if (!(field.input in newValues)) {
            newValues[field.input] =
              fieldKey in updatedCalculatedFields
                ? updatedCalculatedFields[fieldKey]
                : field.portfolioValue ?? "";
          }
        });
        return newValues;
      });
    } else if (activeSection === "projection") {
      setProjectionValues((prev) => {
        const newValues = { ...prev };
        transformedNewFields.forEach((field) => {
          const fieldKey = field.input.toLowerCase();
          if (!(field.input in newValues)) {
            newValues[field.input] =
              fieldKey in updatedCalculatedFields
                ? updatedCalculatedFields[fieldKey]
                : field.portfolioValue ?? "";
          }
        });
        return newValues;
      });
    } else if (activeSection === "outcome") {
      setOutcomeValues((prev) => {
        const newValues = { ...prev };
        transformedNewFields.forEach((field) => {
          const fieldKey = field.input.toLowerCase();
          if (!(field.input in newValues)) {
            newValues[field.input] =
              fieldKey in updatedCalculatedFields
                ? updatedCalculatedFields[fieldKey]
                : field.portfolioValue ?? "";
          }
        });
        return newValues;
      });
    }
  };

  useEffect(() => {
      if (exitIsOpen && entryForms.length > 0) {
        const savedLinkedEntry = exitOverviewList.find((item) => item.input === "linked_entry")?.portfolioValue;
        setLinkedEntry(savedLinkedEntry || entryForms[0]?.formKey || null);
    }
  }, [exitIsOpen, entryForms, exitOverviewList]);

  const handleInputChange = (e, databaseField, expectedValues = []) => {
    if (publishStatus === "published") return;
    const newValue = e.target.value;

    setRawInputs((prev) => ({
      ...prev,
      [databaseField]: newValue,
    }));

    if (expectedValues.length === 0 && newValue !== "" && !/^\d+(\.\d*)?$/.test(newValue)) {
      return;
    }

    clearTimeout(debounceRef.current);
    debounceRef.current = setTimeout(() => {
      updateField(databaseField, newValue);
    }, 500);
  };

  const updateField = async (databaseField, newValue) => {
    if (publishStatus === "published") return;
    if (onSaveStatusChange) {
      onSaveStatusChange("loading");
    }
    const updatedInputValues = { ...inputValues, [databaseField]: newValue };
    const updatedProjectionValues = { ...projectionValues, [databaseField]: newValue };
    const updatedOutcomeValues = { ...outcomeValues, [databaseField]: newValue };

    const allInputs = {
      ...updatedInputValues,
      ...updatedProjectionValues,
      ...updatedOutcomeValues,
    };

    const formulaModeInputs = {};
    Object.entries(allInputs).forEach(([key, value]) => {
      formulaModeInputs[key] = inputMode[key] === false;
    });

    setInputValues(updatedInputValues);
    setProjectionValues(updatedProjectionValues);
    setOutcomeValues(updatedOutcomeValues);

    setTouchedFields((prev) => ({
      ...prev,
      [databaseField]: true,
    }));

    let section = "";
    if (exitOverviewList.some((item) => item.input === databaseField)) section = "overview";
    else if (exitProjectionList.some((item) => item.input === databaseField)) section = "projection";
    else if (exitOutcomeList.some((item) => item.input === databaseField)) section = "outcome";

    if(databaseField === 'linked_entry') {
      section = 'overview';
      setLinkedEntry(newValue);
    }

    try {
      const response = await post("/trade/calculate", {
        inputs: allInputs,
        locked: lockedFields,
        changedField: databaseField,
        formulaModeInputs,
      });

      const updatedFields = response?.calculated || {};

      setUpdatedCalculatedFields((prev) => ({
        ...prev,
        ...updatedFields,
      }));

      Object.entries(updatedFields).forEach(([key, value]) => {
        if (key.toLowerCase() in updatedInputValues)
          updatedInputValues[key.toLowerCase()] = value;
        if (key.toLowerCase() in updatedProjectionValues)
          updatedProjectionValues[key.toLowerCase()] = value;
        if (key.toLowerCase() in updatedOutcomeValues)
          updatedOutcomeValues[key.toLowerCase()] = value;
      });

      setInputValues(updatedInputValues);
      setProjectionValues(updatedProjectionValues);
      setOutcomeValues(updatedOutcomeValues);

      await post("/trade/save-section", {
        formKey,
        section,
        data: [{ input: databaseField, value: newValue, isFormula: inputMode[databaseField] === false }],
      });

      updateDatabasePayload(formKey, {
        section,
        data: {
          ...updatedFields,
          [databaseField.toUpperCase()]: newValue,
        },
      });

      setRawInputs((prev) => {
        const updated = { ...prev };
        delete updated[databaseField];
        return updated;
      });
      
      if (onSaveStatusChange) {
        onSaveStatusChange("success");
      }
    } catch (error) {
      console.error("Error updating field:", error.response?.data || error.message);
      if (onSaveStatusChange) {
        onSaveStatusChange("error");
      }
    }
  };

  const handleMinusClick = (itemToRemove) => {
    if (publishStatus === "published") return;
    setFormData((prevFormData) => {
      const currentList = Array.isArray(prevFormData[formKey]?.[activeSection])
        ? prevFormData[formKey][activeSection]
        : [];

      const updatedList = currentList.filter((item) => item.input !== itemToRemove.input);

      return {
        ...prevFormData,
        [formKey]: {
          ...prevFormData[formKey],
          [activeSection]: updatedList,
        },
      };
    });
  };

  const handlePublishForm = async () => {
    if (publishStatus === "published") return;
    try {
      const response = await post("/trade/publish-form", {
        formKey,
        data: {
          overview: exitOverviewList.map((item) => ({
            input: item.input,
            value: inputValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          projection: exitProjectionList.map((item) => ({
            input: item.input,
            value: projectionValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          outcome: exitOutcomeList.map((item) => ({
            input: item.input,
            value: outcomeValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          notes: notesData,
        },
      });
      console.log("Publish form response:", response);
      setPublishStatus("published");
      setLockedFields((prev) => {
        const newLocks = { ...prev };
        [...exitOverviewList, ...exitProjectionList, ...exitOutcomeList].forEach((item) => {
          newLocks[item.input] = true;
        });
        return newLocks;
      });
    } catch (error) {
      console.error("Error publishing form:", error.response?.data || error.message);
      alert("Failed to publish form. Please try again.");
    }
  };

  const handleDeleteForm = async () => {
      if (publishStatus === "published") return;
      try {
        const response = await post("/trade/delete-form", { formKey });
        if (response.success) {
          if (onDeleteForm) onDeleteForm(formKey);
        } else {
          alert("Failed to delete form. Please try again.");
        }
      } catch (error) {
        console.error("Error deleting form:", error.response?.data || error.message);
        alert("Failed to delete form. Please try again.");
      }
  };

  const createSnapshot = async () => {
    try {
      await post("/trade/create-snapshot", {
        formKey,
        tradeId,
        data: {
          overview: exitOverviewList.map((item) => ({
            input: item.input,
            value: inputValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          projection: exitProjectionList.map((item) => ({
            input: item.input,
            value: projectionValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          outcome: exitOutcomeList.map((item) => ({
            input: item.input,
            value: outcomeValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          notes: notesData,
        },
      });
    } catch (error) {
      console.error("Error creating snapshot:", error.response?.data || error.message);
    }
  };

  const MAX_NOTES_LENGTH = 500;
  const notesRef = useRef(null);

  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    autoResize(notesRef);
  }, [notesData]);

  useEffect(() => {
    const handleResize = () => {
      autoResize(notesRef);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleChartUpload = async (e) => {
    if (publishStatus === "published") return;
    const file = e.target.files?.[0];
    if (!file) return;

    const isValidType = ["image/jpeg", "image/png"].includes(file.type);
    const isValidSize = file.size <= 5 * 1024 * 1024;

    if (!isValidType) {
      alert("Only JPG and PNG files are allowed.");
      return;
    }

    if (!isValidSize) {
      alert("File size must be under 5MB.");
      return;
    }
  };

  const filteredOverviewList = exitOverviewList.filter((item) => item.input !== "transaction_comments");
  const notesField = exitOverviewList.find((item) => item.input === "transaction_comments");

  const renderList = (list, values) => (
    <Row className="g-3 align-items-stretch">
      <Col md={4} sm={6} xs={12} className="d-flex mb-3">
        <div className="trade_builder_card_body_box d-flex flex-column w-100">
          <div className="head h-25 flex-grow-1 d-flex flex-column">
            <div className="d-flex align-items-center gap-2 w-100">
              <CommonTooltip
                className="subTooltip"
                content="Link this Exit to its Entry to calculate position, P&L, and all Entry-based metrics correctly."
                position="top-left"
              >
                <SolidInfoIcon />
              </CommonTooltip>
              <h5 className="w-100">LINKED ENTRY</h5>
            </div>
          </div>
          <div className="show_metrics_dimenstion">
            <span>TRADE:</span>
            <span>METRIC</span>
          </div>
          <div className="d-flex flex-column justify-content-end flex-grow-1">
            <select
              value={linkedEntry || ""}
              onChange={(e) => updateField("linked_entry", e.target.value)}
              className="dropdown-select w-100"
              style={{
                backgroundColor: publishStatus !== "published" ? "#fff" : "#004080",
                color: publishStatus !== "published" ? "#000" : "#fff",
                border: !linkedEntry && publishStatus !== "published" ? "2px solid red" : "1px solid #ccc",
                borderRadius: "4px",
                padding: "6px",
                fontSize: "14px",
                height: "40px",
              }}
              disabled={publishStatus === "published" || lockedFields["linked_entry"]}
            >
              <option value="">Select Entry</option>
              {entryForms.map((entry) => (
                <option key={entry.formKey} value={entry.formKey}>
                  Entry {entry.index}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Col>
      {list.map((item, index) => (
        <Col key={item.input} md={4} sm={6} xs={12} className="d-flex">
          <div className="trade_builder_card_body_box d-flex flex-column w-100">
            <div className="head h-25 flex-grow-1 d-flex flex-column">
              <div className="d-flex align-items-center gap-2 w-100">
                <CommonTooltip
                  className="subTooltip"
                  content={
                    <>
                      {item?.tooltip}
                      {item?.input && (
                        <>
                          {" "}
                          <a
                            href={`/education/${item.input.replace(/^portfolio_|^transaction_/, "").replace(/_/g, "-")}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ color: "#00bfff", textDecoration: "underline" }}
                          >
                            Read more
                          </a>
                        </>
                      )}
                    </>
                  }
                  position="top-left"
                >
                  <SolidInfoIcon />
                </CommonTooltip>
                <h5 className="w-100">{item?.title}</h5>
                <div className="d-flex align-items-center justify-end">
                  {item.is_editable && item.has_formula && item.account_field !== "YES" && publishStatus !== "published" && (
                    <label className="switch">
                      <input
                        type="checkbox"
                        checked={inputMode[item.input] === false}
                        onChange={() => confirmToggleInputMode(item.input)}
                      />
                      <span className="slider round"></span>
                    </label>
                  )}
                  {item.show_icon && publishStatus !== "published" && (
                    <div
                      className="d-flex align-items-center justify-content-center"
                      style={{ height: "22px", width: "25px", cursor: "pointer" }}
                      onClick={() => handleMinusClick(item)}
                    >
                      <MinusIcon height="15px" width="15px" />
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="show_metrics_dimenstion">
              <span>
                {item.input?.toLowerCase().startsWith("transaction_")
                  ? "TRANSACTION:"
                  : item.input?.toLowerCase().startsWith("trade_")
                  ? "TRADE:"
                  : item.input?.toLowerCase().startsWith("portfolio_")
                  ? "PORTFOLIO:"
                  : ""}
              </span>
              <span>{item.metric_dimension === "dimension" ? "DIMENSION" : "METRIC"}</span>
            </div>
            <div className="d-flex flex-column justify-content-end flex-grow-1">
              {item.is_editable && item?.expected_values?.length > 0 && item.input !== "transaction_leverage_factor" && item.account_field !== "YES" ? (
                <select
                  value={values[item.input] || ""}
                  onChange={(e) => handleInputChange(e, item.input, item.expected_values)}
                  className="dropdown-select w-100"
                  style={{
                    backgroundColor: item.is_editable && publishStatus !== "published" ? "white" : "black",
                    color: item.is_editable && publishStatus !== "published" ? "black" : "white",
                  }}
                  disabled={!item.is_editable || lockedFields[item.input] || publishStatus === "published"}
                >
                  <option value="">Select</option>
                  {item.expected_values.map((val) => (
                    <option key={val} value={val}>
                      {val}
                    </option>
                  ))}
                </select>
              ) : (
                <div className="input-container flex-grow-1 position-relative d-flex flex-column">
                  <input
                    type="text"
                    className="input-field flex-grow-1"
                    value={
                      rawInputs[item.input] !== undefined
                        ? rawInputs[item.input]
                        : item?.is_editable
                        ? formatValue(values[item.input], item?.datatype, blurredFields[item.input])
                        : item.account_field === "YES"
                        ? formatValue(item?.portfolioValue, item?.datatype, true)
                        : formatValue(values[item.input], item?.datatype, blurredFields[item.input])
                    }
                    style={{
                      backgroundColor:
                        !item.is_editable || item.account_field === "YES" || publishStatus === "published"
                          ? "#004080"
                          : inputMode[item.input] !== false
                          ? "white"
                          : "#004080",
                      color:
                        !item.is_editable || item.account_field === "YES" || publishStatus === "published"
                          ? "white"
                          : inputMode[item.input] !== false
                          ? "black"
                          : "white",
                      paddingRight: inputMode[item.input] === false ? "30px" : "12px",
                    }}
                    onChange={(e) => handleInputChange(e, item.input, item.expected_values)}
                    onBlur={() => handleBlur(item.input)}
                    onFocus={() => handleFocus(item.input)}
                    readOnly={
                      !item.is_editable ||
                      lockedFields[item.input] ||
                      inputMode[item.input] === false ||
                      item.account_field === "YES" ||
                      publishStatus === "published"
                    }
                  />
                  {(inputMode[item.input] === false || (item.has_formula && !item.is_editable)) && (
                    <div style={{ position: "absolute", right: "10px", top: "50%", transform: "translateY(-50%)" }}>
                      <CommonTooltip
                        className="subTooltip"
                        content="💡 This field is calculated using TradeReply’s proprietary formula."
                        position="top-left"
                      >
                        <img
                          src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-formula-icon.svg"
                          alt="Formula"
                          className="formula-icon"
                          style={{ width: "14px", height: "14px", opacity: 0.7 }}
                        />
                      </CommonTooltip>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </Col>
      ))}
    </Row>
  );

  return (
    <>
      <div>
        <div className="trade_builder_card_head">
          <AdminHeading heading={`Exit ${index} Analysis`} centered />
          <button type="button" className="trade_builder_card_head_btnArrow" onClick={toggleExitCollapse}>
            {exitIsOpen ? <DropArrowUpIcon /> : <DropArrowIcon />}
          </button>
        </div>
        {exitIsOpen && (
          <div
            className="trade_builder_card_body_wrapper"
            style={{ height: height || "auto", overflow: "hidden", transition: "height 0.5s ease" }}
            ref={contentRef}
          >
            {publishStatus === "draft" && (
              <div className="trade_manager_btns">
                <CommonButton
                  title="Publish Form"
                  className="green-btn w-100 mt-3"
                  onClick={handlePublishForm}
                />
                <CommonButton
                  title="Delete"
                  className="red-btn w-100 mt-3 me-2"
                  onClick={handleDeleteForm}
                />
              </div>
            )}
            <div className="relative trade_builder_card_body_notes">
              <div className="trade_builder_card_body_box d-flex flex-column w-100 mb-1 mt-3">
                <div className="head h-25 flex-grow-1 d-flex flex-column p-2">
                  <div className="d-flex align-items-center gap-2 w-100">
                    <CommonTooltip
                      className="subTooltip"
                      content={
                        <>
                          {notesField?.summary ||
                            notesField?.tooltip ||
                            "Transaction-Level Comments capture trader notes specific to individual transactions, providing detailed context for entry and exit decisions."}
                          {notesField?.input && (
                            <>
                              {" "}
                              <a
                                href={`/education/${notesField.input.replace(/^portfolio_|^transaction_/, "").replace(/_/g, "-")}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ color: "#00bfff", textDecoration: "underline" }}
                              >
                                Read more
                              </a>
                            </>
                          )}
                        </>
                      }
                      position="top-left"
                    >
                      <SolidInfoIcon />
                    </CommonTooltip>
                    <h5 className="w-100">Comments</h5>
                  </div>
                </div>
                <div className="show_metrics_dimenstion p-2">
                  <span>TRANSACTION:</span>
                  <span>METRIC</span>
                </div>
                <div className="input-container flex-grow-1 position-relative d-flex flex-column">
                  <textarea
                    className="form-textarea w-full resize-none overflow-hidden"
                    style={{
                      backgroundColor: publishStatus === "published" ? "#004080" : "#4a91c6",
                      color: "#f4F4F4",
                      fontSize: "18px",
                      fontWeight: "bold",
                      borderRadius: "none !important",
                      borderBottomLeftRadius: "10px",
                      borderBottomRightRadius: "10px",
                    }}
                    rows="4"
                    placeholder="Leave a note about this entry or exit (optional)..."
                    ref={notesRef}
                    maxLength={MAX_NOTES_LENGTH}
                    value={notesData}
                    onChange={handleNotesChange}
                    onBlur={() => {
                      if (publishStatus === "published") return;
                      if (notesData.trim() && notesData !== lastSavedNote) {
                        saveNotes(notesData);
                      }
                    }}
                    readOnly={publishStatus === "published"}
                  ></textarea>
                </div>
              </div>
              <div className="character-count">
                Characters left: {notesData.length}/{MAX_NOTES_LENGTH}
              </div>
            </div>
            <div className="trade_manager_btns">
              <input type="file" accept=".jpg,.jpeg,.png" style={{ display: "none" }} ref={fileInputRef} onChange={handleChartUpload} />
              <CommonButton
                title="Upload Market Chart"
                onClick={() => fileInputRef.current?.click()}
                className="w-100 mt-3"
                disabled={publishStatus === "published"}
              />
            </div>
            <div className="trade_builder_card_body">
              {renderList(filteredOverviewList, inputValues)}
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon />}
                  className="w-100 mt-3"
                  onClick={() => {
                    setActiveSection("overview");
                    openDimentionModal();
                  }}
                  disabled={publishStatus === "published"}
                />
              </div>
              <div className="trade_builder_card_head mt-4">
                <AdminHeading heading="Overview (Projection)" centered />
              </div>
              <div className="trade_builder_card_body">
                {renderList(exitProjectionList, projectionValues)}
              </div>
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon
             />}
                  className="w-100 mt-3"
                  onClick={() => {
                    setActiveSection("projection");
                    openDimentionModal();
                  }}
                  disabled={publishStatus === "published"}
                />
              </div>
              <div className="trade_builder_card_head mt-4">
                <AdminHeading heading="Overview (Post Trade)" centered />
              </div>
              <div className="trade_builder_card_body">
                {renderList(exitOutcomeList, outcomeValues)}
              </div>
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon />}
                  className="w-100 mt-3"
                  onClick={() => {
                    setActiveSection("outcome");
                    openDimentionModal();
                  }}
                  disabled={publishStatus === "published"}
                />
              </div>
            </div>
          </div>
        )}
      </div>
      {showConfirmModal && (
        <div className="inset-0 z-50 modal_overlay">
          <div className="search_section h-auto">
            <h2 className="h4 font-semibold mb-4">
              Only one of these fields can be manually entered at a time.
            </h2>
            {pendingToggleField && (
              (() => {
                const isManual = !inputMode[pendingToggleField];
                const action = isManual ? "Manual Entry" : "Auto-Calculate";
                const oppositeAction = isManual ? "Auto-Calculate" : "Manual Entry";
                const fieldLabel =
                  pendingToggleField === "transaction_risk_percentage" ? "Risk Percentage" : "Quantity Purchased";
                const otherLabel =
                  pendingToggleField === "transaction_risk_percentage" ? "Quantity Purchased" : "Risk Percentage";
                return (
                  <p className="mb-4 h5 font-regular text-gray-600">
                    Toggling <strong>{fieldLabel}</strong> to <strong>{action}</strong> will switch <strong>{otherLabel}</strong> to{" "}
                    <strong>{oppositeAction}</strong>.
                  </p>
                );
              })()
            )}
            <div className="flex justify-end gap-3 mt-3">
              <CommonButton title="Cancel" className="gray-btn p-2 rounded-4 lh-sm" onClick={handleCancelToggle} />
              <CommonButton title="Confirm" className="btn-primary p-2 rounded-4 lh-sm" onClick={handleConfirmToggle} />
            </div>
          </div>
        </div>
      )}
      {isDimentionModal && (
        <ConfigScopeModal
          transactionFields={transactionFields}
          tradeFields={tradeFields}
          portfolioFields={portfolioFields}
          onClose={closeDimentionModal}
          onConfirm={handleAddFields}
        />
      )}
    </>
  );
}