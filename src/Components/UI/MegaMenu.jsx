'use client';
import React, { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import { ChevronRight, ChevronDown } from 'lucide-react';

export default function MegaMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState(null);
  const [categories, setCategories] = useState([]);
  const [isMobileView, setIsMobileView] = useState(false);
  const [mobileStep, setMobileStep] = useState('categories');
  const menuRef = useRef(null);

  useEffect(() => {
    const fetchData = async () => {
      const res = await fetch('/tradereply-categories.json');
      const data = await res.json();
      setCategories(data.categories);
    };
    fetchData();
  }, []);

  useEffect(() => {
    const updateMobileView = () => {
      setIsMobileView(window.innerWidth < 728);
    };
    updateMobileView();
    window.addEventListener('resize', updateMobileView);
    return () => window.removeEventListener('resize', updateMobileView);
  }, []);

  // ✅ Close when clicking outside the menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
        setActiveCategory(null);
        setMobileStep('categories');
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleMobileCategoryClick = (cat) => {
    setActiveCategory(cat);
    setMobileStep('subcategories');
  };

  const handleDesktopHover = (cat) => {
    setActiveCategory(cat);
  };

  const handleClose = () => {
    setIsOpen(false);
    setActiveCategory(null);
    setMobileStep('categories');
  };

  const handleBack = () => {
    setMobileStep('categories');
    setActiveCategory(null);
  };

  return (
    <div
      className="megaMenu"
      ref={menuRef}
      onMouseLeave={() => {
        if (!isMobileView) setActiveCategory(null);
      }}
    >
      <button onClick={() => setIsOpen(!isOpen)} className="megaMenu__toggle">
        Shop <ChevronDown size={18} />
      </button>

      {isOpen && (
        <div className={`megaMenu__dropdown ${activeCategory ? 'expanded' : ''}`}>
          {isMobileView && (
            <div className="megaMenu__mobile-header">
              {mobileStep === 'subcategories' && (
                <button className="megaMenu__back" onClick={handleBack}>←</button>
              )}
              <span className="megaMenu__logo">Shop</span>
              <button className="megaMenu__close" onClick={handleClose}>✕</button>
            </div>
          )}

          {(!isMobileView || mobileStep === 'categories') && (
            <div className="megaMenu__categories">
              {categories.map((cat) => (
                <div
                  key={cat.slug}
                  className={`megaMenu__category ${activeCategory?.slug === cat.slug ? 'active' : ''}`}
                  onMouseEnter={() => !isMobileView && handleDesktopHover(cat)}
                  onClick={() => isMobileView && handleMobileCategoryClick(cat)}
                >
                  <span className="megaMenu__category-link">
                    {cat.name}
                    <ChevronRight className="megaMenu__icon" />
                  </span>
                </div>
              ))}
            </div>
          )}

          {(!isMobileView || mobileStep === 'subcategories') && activeCategory && (
            <div className="megaMenu__subcategories visible">
              <div className="megaMenu__columns">
                {activeCategory.subcategories.map((sub) => (
                  <div key={sub.slug} className="megaMenu__column">
                    {/* <p className="megaMenu__subtitle">{sub.name}</p> */}
                    <Link
                      href={`/marketplace/categories/${activeCategory.slug}/${sub.slug}`}
                      className="megaMenu__subtitle"
                    >
                      {sub.name}
                    </Link>
                    <ul className="megaMenu__items">
                      {sub.subcategories.map((item) => (
                        <li key={item.slug}>
                          <Link
                            href={`/marketplace/categories/${activeCategory.slug}/${sub.slug}/${item.slug}`}
                            className="megaMenu__link"
                          >
                            {item.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
