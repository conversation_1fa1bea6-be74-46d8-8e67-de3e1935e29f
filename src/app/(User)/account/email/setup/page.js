'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import StatusIndicator from "@/Components/UI/StatusIndicator";
import { put, get } from "@/utils/apiUtils";
import { useRouter, useSearchParams } from 'next/navigation';
import { useSelector } from 'react-redux';
import { hashInput } from "@/utils/hashInput";
import { v4 as uuidv4 } from "uuid";
import "@/css/account/AccountDetails.scss";

export default function SetupEmail() {
    const [email, setEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [saveStatus, setSaveStatus] = useState(null);
    const [error, setError] = useState(null);
    const [userData, setUserData] = useState(null);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);

    const router = useRouter();
    const searchParams = useSearchParams();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Fetch user data to pre-populate current email
    const fetchUserData = async () => {
        try {
            setIsLoadingUserData(true);
            const response = await get('/account');

            if (response.success && response.data) {
                const existingEmail = response.data.email || '';
                setCurrentEmail(existingEmail);
                setUserData(response.data);
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            // Fallback to Redux user data
            if (reduxUser?.email) {
                setCurrentEmail(reduxUser.email);
                setUserData(reduxUser);
            }
        } finally {
            setIsLoadingUserData(false);
        }
    };

    useEffect(() => {
        fetchUserData();
    }, []);

    // Get redirect URL (similar to other account setup patterns)
    const getRedirectUrl = () => {
        // Check URL parameters first
        const from = searchParams.get('from');
        if (from) {
            try {
                return decodeURIComponent(from);
            } catch (e) {
                console.warn('Failed to decode from parameter:', from);
            }
        }

        // Check document referrer as fallback
        if (typeof window !== 'undefined' && document.referrer) {
            try {
                const referrerUrl = new URL(document.referrer);
                // Only use referrer if it's from the same origin
                if (referrerUrl.origin === window.location.origin) {
                    return referrerUrl.pathname + referrerUrl.search;
                }
            } catch (e) {
                console.warn('Failed to parse document referrer:', document.referrer);
            }
        }

        // Default fallback
        return '/account/details';
    };

    const metaArray = {
        noindex: true,
        title: "Setup Email Address | Update Info | TradeReply",
        description: "Update your email address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Setup Email Address | Update Info | TradeReply",
        og_description: "Update your email address on TradeReply.com.",
        twitter_title: "Setup Email Address | Update Info | TradeReply",
        twitter_description: "Update your email address on TradeReply.com.",
    };

    const handleSave = async () => {
        // Validation
        if (!currentEmail.trim()) {
            setSaveStatus('error');
            setError('Current email address is required');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        if (!newEmail.trim()) {
            setSaveStatus('error');
            setError('New email address is required');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(newEmail.trim())) {
            setSaveStatus('error');
            setError('Please enter a valid email address');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        // Check if new email is different from current
        if (currentEmail.trim().toLowerCase() === newEmail.trim().toLowerCase()) {
            setSaveStatus('error');
            setError('Please enter a different email address');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);

            // Step 1: Request email update with current and new email
            const response = await put(`/account/update/${userData?.id}`, {
                email: currentEmail.trim(),
                new_email: newEmail.trim(),
            });

            if (response.success) {
                // Generate UUID for session tracking
                const uuid = uuidv4();
                const expiresInMinutes = 15;
                const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;

                // Store email update data in sessionStorage
                sessionStorage.setItem("masked_email", hashInput(newEmail.trim()));
                sessionStorage.setItem(
                    "email_update_data",
                    JSON.stringify({
                        uuid,
                        expiresAt,
                        new_email: newEmail.trim(),
                        current_email: currentEmail.trim(),
                        user_id: userData?.id
                    })
                );

                // Store redirect URL for after verification
                const redirectUrl = getRedirectUrl();
                sessionStorage.setItem("email_update_redirect", redirectUrl);

                // Redirect to security-check for email verification
                router.push(`/security-check?emailUpdate=email_update_data`);
            } else {
                throw new Error(response.message || 'Failed to initiate email update');
            }
        } catch (err) {
            console.error('Email update error:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Failed to update email address. Please try again.';
            setSaveStatus('error');
            setError(errorMessage);
            setTimeout(() => setSaveStatus(null), 3000);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        // Reset form state
        setNewEmail('');
        setError(null);
        setSaveStatus(null);

        // Navigate back to the original page
        const redirectUrl = getRedirectUrl();
        router.push(redirectUrl);
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title="New Email Address" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Email</h6>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                            />
                                        </div>
                                    </div>
                                    <p>Enter your current email address and the new email address you want associated with your TradeReply account. You will receive a verification code at your new email address.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    {isLoadingUserData ? (
                                        <div className="text-center py-3">
                                            <span>Loading current email...</span>
                                        </div>
                                    ) : (
                                        <>
                                            <div className="col-lg-5 col-md-8 col-12 mb-3">
                                                <label className="form-label">Current Email Address</label>
                                                <TextInput
                                                    type="email"
                                                    placeholder="Current email address"
                                                    value={currentEmail}
                                                    onChange={(e) => setCurrentEmail(e.target.value)}
                                                    disabled={isLoading}
                                                />
                                            </div>
                                            <div className="col-lg-5 col-md-8 col-12">
                                                <label className="form-label">New Email Address</label>
                                                <TextInput
                                                    type="email"
                                                    placeholder="Enter your new email address"
                                                    value={newEmail}
                                                    onChange={(e) => setNewEmail(e.target.value)}
                                                    disabled={isLoading}
                                                />
                                            </div>
                                        </>
                                    )}

                                    {error && saveStatus !== 'loading' && (
                                        <div className="mt-3">
                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                {error}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button
                                className="btn-style white-btn"
                                onClick={handleCancel}
                                disabled={isLoading}
                            >
                                Cancel
                            </button>
                            <button
                                className="btn-style"
                                onClick={handleSave}
                                disabled={isLoading || isLoadingUserData || !currentEmail.trim() || !newEmail.trim()}
                            >
                                {isLoading ? 'Sending Verification...' : 'Update Email'}
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
