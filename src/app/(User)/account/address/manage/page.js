'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import "@/css/account/AccountDetails.scss";
import CustomDropdown from "@/Components/common/CustumDropdown";
import countries from 'world-countries';
import Link from "next/link";
import { EditIconSvg, PlusIconSvg, CheckIcon, RemoveIconSvg } from '@/assets/svgIcons/SvgIcon';
import { get, put, del, post } from "@/utils/apiUtils";
import { maskFullName, maskAddress } from "@/utils/addressMask";
import { useRouter, useSearchParams } from 'next/navigation';
import StatusIndicator from "@/Components/UI/StatusIndicator";

export default function ManageAddresses() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const addressId = searchParams.get('id');
    const action = searchParams.get('action');

    const [addresses, setAddresses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [saveStatus, setSaveStatus] = useState(null);

    // Edit form states
    const [editingAddressId, setEditingAddressId] = useState(null);
    const [editFormData, setEditFormData] = useState({
        first_name: '',
        last_name: '',
        address: '',
        city: '',
        state: '',
        zip_code: '',
        country: '',
        is_default: false
    });
    const [selectedCountry, setSelectedCountry] = useState('');

    const [showConfirm, setShowConfirm] = useState(null);

    // Fetch addresses from backend
    const fetchAddresses = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await get('/addresses');

            if (response.success && response.data) {
                setAddresses(response.data);
            } else {
                throw new Error(response.message || 'Failed to fetch addresses');
            }
        } catch (err) {
            console.error('Error fetching addresses:', err);
            setError(err.message || 'Failed to load addresses');
        } finally {
            setLoading(false);
        }
    };

    // Handle address deletion
    const handleDeleteAddress = async (addressId) => {
        try {
            setSaveStatus('loading');
            const response = await del(`/addresses/${addressId}`);

            if (response.success) {
                setSaveStatus('success');
                await fetchAddresses(); // Refresh the list
                setShowConfirm(null);
                setTimeout(() => setSaveStatus(null), 2000);
            } else {
                throw new Error(response.message || 'Failed to delete address');
            }
        } catch (err) {
            console.error('Error deleting address:', err);
            setError(err.message || 'Failed to delete address');
            setSaveStatus('error');
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    // Handle address update
    const handleUpdateAddress = async () => {
        try {
            setSaveStatus('loading');

            const response = await put(`/addresses/${editingAddressId}`, {
                first_name: editFormData.first_name,
                last_name: editFormData.last_name,
                address: editFormData.address,
                city: editFormData.city,
                state: editFormData.state,
                zip_code: editFormData.zip_code,
                country: editFormData.country,
                is_default: editFormData.is_default
            });

            if (response.success) {
                setSaveStatus('success');
                await fetchAddresses(); // Refresh the list
                setEditingAddressId(null);
                setTimeout(() => setSaveStatus(null), 2000);
            } else {
                throw new Error(response.message || 'Failed to update address');
            }
        } catch (err) {
            console.error('Error updating address:', err);
            setError(err.message || 'Failed to update address');
            setSaveStatus('error');
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    // Handle setting address as default
    const handleSetDefault = async (addressId) => {
        try {
            setSaveStatus('loading');
            const response = await post(`/addresses/${addressId}/set-default`);

            if (response.success) {
                setSaveStatus('success');
                await fetchAddresses(); // Refresh the list
                setTimeout(() => setSaveStatus(null), 2000);
            } else {
                throw new Error(response.message || 'Failed to set default address');
            }
        } catch (err) {
            console.error('Error setting default address:', err);
            setError(err.message || 'Failed to set default address');
            setSaveStatus('error');
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    const selectCountry = (country) => {
        setSelectedCountry(country.name.common);
        setEditFormData(prev => ({ ...prev, country: country.name.common }));
    };

    // Handle URL parameters for edit/remove actions
    useEffect(() => {
        fetchAddresses();
    }, []);

    useEffect(() => {
        if (addressId && action && addresses.length > 0) {
            const address = addresses.find(addr => addr.id === parseInt(addressId));
            if (address && action === 'edit') {
                // Start editing mode
                setEditingAddressId(address.id);
                setEditFormData({
                    first_name: address.first_name || '',
                    last_name: address.last_name || '',
                    address: address.address || '',
                    city: address.city || '',
                    state: address.state || '',
                    zip_code: address.zip_code || '',
                    country: address.country || '',
                    is_default: address.is_default || false
                });
                setSelectedCountry(address.country || '');
            } else if (address && action === 'remove') {
                // Show confirmation dialog
                setShowConfirm(address.id);
            }
        }
    }, [addressId, action, addresses]);

    const handleDelete = (id) => {
        handleDeleteAddress(id);
    };
    const metaArray = {
        noindex: true,
        title: "Manage Your Addresses | Update Info | TradeReply",
        description: "Manage your address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Manage Your Addresses | Update Info | TradeReply",
        og_description: "Manage your address on TradeReply.com.",
        twitter_title: "Manage Your Addresses | Update Info | TradeReply",
        twitter_description: "Manage your address on TradeReply.com.",
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title="Manage Your Addresses" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Address Book</h6>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div className="common_blackcard_innerheader_icon">
                                    <Link href={`/account/address/setup?from=${encodeURIComponent('/account/address/manage')}`} prefetch={true}>
                                        <button className="d-flex align-items-center">
                                            <PlusIconSvg />
                                            <span className="ms-2">Add New Address</span>
                                        </button>
                                    </Link>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list new-address-section">
                                    <ul>
                                        {[...addresses]
                                            .sort((a, b) => (b.isDefault === true) - (a.isDefault === true))
                                            .map((addr) => (
                                                <React.Fragment key={addr.id}>
                                                    <li
                                                        className={editingAddressId === addr.id ? 'border-bottom-none' : ''}
                                                    >
                                                        <Col xs={12} md={3}>
                                                            {addr.isDefault ? (
                                                                <div className="d-flex align-items-center gap-2">
                                                                    <CheckIcon />
                                                                    <span className="green_text">Default</span>
                                                                </div>
                                                            ) : (
                                                                <button>
                                                                    <span className='text_00ADEF'>Set as Default</span>
                                                                </button>
                                                            )}
                                                        </Col>
                                                        <Col xs={12} md={9}>
                                                            <div className='d-flex justify-content-between align-items-center w-100'>
                                                                <div className='show-address-details'>
                                                                    <p className='name'>{maskName(addr.firstName)}{maskName(addr.lastName)}</p>
                                                                    <p className='address'>{maskAddress(addr.address)}</p>
                                                                    <p className='city'>{addr.city}, {addr.state}, {addr.zip}</p>

                                                                    {showConfirm === addr.id && (
                                                                        <div className='remove-address-confirmation mt-2'>
                                                                            <p>Are you sure you want to remove this address?</p>
                                                                            <div className="btns d-flex gap-2">
                                                                                <button className="btn-style gray-btn" onClick={() => setShowConfirm(null)}>
                                                                                    Cancel
                                                                                </button>
                                                                                <button className="btn-style red-btn" onClick={() => handleDelete(addr.id)}>
                                                                                    Remove
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                    )}
                                                                </div>

                                                                {editingAddressId !== addr.id && (
                                                                    <div className='btns d-flex gap-4'>
                                                                        {!addr.isDefault && showConfirm !== addr.id && (
                                                                            <button className="d-flex align-items-center" onClick={() => setShowConfirm(addr.id)}>
                                                                                <RemoveIconSvg />
                                                                                <span className="ms-1">Remove</span>
                                                                            </button>
                                                                        )}
                                                                        <button
                                                                            className="d-flex align-items-center"
                                                                            onClick={() => {
                                                                                setEditingAddressId(addr.id);
                                                                                setEditFormData({ ...addr });
                                                                            }}
                                                                        >
                                                                            <EditIconSvg />
                                                                            <span className="ms-1">Edit</span>
                                                                        </button>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </Col>
                                                    </li>

                                                    {editingAddressId === addr.id && (
                                                        <li>
                                                            <div className="row">
                                                                <Col xs={12} md={3} />
                                                                <Col xs={12} md={5}>
                                                                    <div className="row">
                                                                        <Col xs={12} className="mb-4">
                                                                            <CustomDropdown
                                                                                options={countries.map((c) => ({ label: c.name.common, ...c }))}
                                                                                defaultValue={editFormData.country || "Select Country"}
                                                                                onSelect={(c) => setEditFormData({ ...editFormData, country: c.name.common })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={6}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="First Name"
                                                                                value={editFormData.firstName}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, firstName: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={6}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="Last Name"
                                                                                value={editFormData.lastName}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, lastName: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={12}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="Address"
                                                                                value={editFormData.address}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, address: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={12}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="City"
                                                                                value={editFormData.city}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, city: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={12}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="State"
                                                                                value={editFormData.state}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, state: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <Col xs={12}>
                                                                            <TextInput
                                                                                type="text"
                                                                                placeholder="Zip"
                                                                                value={editFormData.zip}
                                                                                onChange={(e) => setEditFormData({ ...editFormData, zip: e.target.value })}
                                                                            />
                                                                        </Col>
                                                                        <div className="custom_checkbox my-2">
                                                                            <input
                                                                                className="custom_checkbox_input form-check-input"
                                                                                type="checkbox"
                                                                                id="defaultAddress"
                                                                                checked={editFormData.isDefault}
                                                                                onChange={(e) =>
                                                                                    setEditFormData({
                                                                                        ...editFormData,
                                                                                        isDefault: e.target.checked,
                                                                                    })
                                                                                }
                                                                            />
                                                                            <label className="custom_checkbox_label" htmlFor="defaultAddress">
                                                                                Set as default address
                                                                            </label>
                                                                        </div>
                                                                        <div className="account_card_list_btns">
                                                                            <button
                                                                                className="btn-style white-btn"
                                                                                onClick={() => setEditingAddressId(null)}
                                                                            >
                                                                                Cancel
                                                                            </button>
                                                                            <button
                                                                                className="btn-style"
                                                                                onClick={() => handleUpdateAddress()}
                                                                            >
                                                                                Save
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </Col>
                                                            </div>
                                                        </li>
                                                    )}
                                                </React.Fragment>
                                            ))}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
