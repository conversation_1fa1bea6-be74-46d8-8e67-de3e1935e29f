'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import StatusIndicator from "@/Components/UI/StatusIndicator";
import "@/css/account/AccountDetails.scss";
import CustomDropdown from "@/Components/common/CustumDropdown";
import countries from 'world-countries';
import { post } from "@/utils/apiUtils";
import { useRouter, useSearchParams } from 'next/navigation';

export default function SetupNewAddress() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const from = searchParams.get('from');

    const [country, setCountry] = useState('');
    const [selectedCountry, setSelectedCountry] = useState(country);
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [address, setAddress] = useState('');
    const [addressLine2, setAddressLine2] = useState('');
    const [city, setCity] = useState('');
    const [state, setState] = useState('');
    const [zipCode, setZipCode] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [saveStatus, setSaveStatus] = useState(null);
    const [error, setError] = useState(null);

    const selectCountry = (country) => {
        console.log('Selected:', country);
        setSelectedCountry(country.name.common);
    };

    const handleSave = async () => {
        // Basic validation
        if (!selectedCountry || !firstName.trim() || !lastName.trim() || !address.trim() || !city.trim() || !state.trim() || !zipCode.trim()) {
            setSaveStatus('error');
            setError('Please fill in all required fields');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);

            // Combine address and address line 2
            const fullAddress = addressLine2.trim()
                ? `${address.trim()}, ${addressLine2.trim()}`
                : address.trim();

            const response = await post('/addresses', {
                country: selectedCountry,
                first_name: firstName.trim(),
                last_name: lastName.trim(),
                address: fullAddress,
                city: city.trim(),
                state: state.trim(),
                zip_code: zipCode.trim(),
                is_default: false // Let backend handle default logic
            });

            if (response.success) {
                setSaveStatus('success');

                // Navigate back to the referring page or default to account details
                setTimeout(() => {
                    const redirectTo = from ? decodeURIComponent(from) : '/account/details';
                    router.push(redirectTo);
                }, 1500);
            } else {
                throw new Error(response.message || 'Failed to save address');
            }
        } catch (err) {
            console.error('Address save error:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Failed to save address. Please try again.';
            setSaveStatus('error');
            setError(errorMessage);
            setTimeout(() => setSaveStatus(null), 3000);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        // Navigate back to the referring page or default to account details
        const redirectTo = from ? decodeURIComponent(from) : '/account/details';
        router.push(redirectTo);
    };
    const metaArray = {
        noindex: true,
        title: "Add New Address | Update Info | TradeReply",
        description: "Add new address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Add New Address | Update Info | TradeReply",
        og_description: "Add new address on TradeReply.com.",
        twitter_title: "Add New Address | Update Info | TradeReply",
        twitter_description: "Add new address on TradeReply.com.",
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title="Add New Address" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>New Address</h6>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list row col-5">
                                    <Col xs={12} className="mb-4">
                                        <CustomDropdown
                                            options={countries.map((c) => ({ label: c.name.common, ...c }))}
                                            defaultValue="Select Country"
                                            onSelect={selectCountry}
                                        />
                                    </Col>
                                    <Col xs={6}>
                                        <TextInput
                                            type="text"
                                            placeholder="First Name"
                                            value={firstName}
                                            onChange={(e) => setFirstName(e.target.value)}
                                            disabled={isLoading}
                                        />
                                    </Col>
                                    <Col xs={6}>
                                        <TextInput
                                            type="text"
                                            placeholder="Last Name"
                                            value={lastName}
                                            onChange={(e) => setLastName(e.target.value)}
                                            disabled={isLoading}
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="Address"
                                            value={address}
                                            onChange={(e) => setAddress(e.target.value)}
                                            disabled={isLoading}
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="Address Line 2 (Optional)"
                                            value={addressLine2}
                                            onChange={(e) => setAddressLine2(e.target.value)}
                                            disabled={isLoading}
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="City"
                                            value={city}
                                            onChange={(e) => setCity(e.target.value)}
                                            disabled={isLoading}
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="State or Region"
                                            value={state}
                                            onChange={(e) => setState(e.target.value)}
                                            disabled={isLoading}
                                        />
                                    </Col>
                                    <Col xs={12}>
                                        <TextInput
                                            type="text"
                                            placeholder="Zip or Postal Code"
                                            value={zipCode}
                                            onChange={(e) => setZipCode(e.target.value)}
                                            disabled={isLoading}
                                        />
                                    </Col>

                                    {error && saveStatus !== 'loading' && (
                                        <Col xs={12}>
                                            <div className="mt-3">
                                                <p style={{ color: 'red', fontSize: '14px' }}>
                                                    {error}
                                                </p>
                                            </div>
                                        </Col>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button
                                className="btn-style white-btn"
                                onClick={handleCancel}
                                disabled={isLoading}
                            >
                                Cancel
                            </button>
                            <button
                                className="btn-style"
                                onClick={handleSave}
                                disabled={isLoading || !selectedCountry || !firstName.trim() || !lastName.trim() || !address.trim() || !city.trim() || !state.trim() || !zipCode.trim()}
                            >
                                {isLoading ? 'Saving...' : 'Save'}
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
