"use client";

import { Col, Container, Row } from "react-bootstrap";
import { useRouter } from "next/navigation";
import { PlusIcon, SolidRedArrowIcon } from "@/assets/svgIcons/SvgIcon";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import CommonButton from "@/Components/UI/CommonButton";
import DashboardLayout from "@/Layouts/DashboardLayout";
import "@/css/dashboard/TradeBuilder.scss";
import TradeBuilderEntry from "@/Components/common/TradeBuilder/TradeBuilderEntry";
import TradeBuilderExit from "@/Components/common/TradeBuilder/TradeBuilderExit";
import MetaHead from "@/Seo/Meta/MetaHead";
import { get, post } from "@/utils/apiUtils";
import { useEffect, useState, useRef, Suspense } from "react";
import { useSearchParams } from "next/navigation";

const TradeBuilderLoading = () => (
  <DashboardLayout>
    <div className="trade_manager trade_builder">
      <CommonHead isShowCalender="false" />
      <Container>
        <Row className="trade_head align-items-center pt-4 pb-6">
          <Col md={4}><AdminHeading heading="Trade Builder" centered /></Col>
          <Col md={4}><div className="trade_head_title"><h4>Loading...</h4></div></Col>
        </Row>
        <div className="text-center py-5">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </Container>
    </div>
  </DashboardLayout>
);

const TradeBuilder = () => {
  const searchParams = useSearchParams();
  const tradeIdParam = searchParams.get("tradeId");
  const [tradeForms, setTradeForms] = useState([{ id: 1, type: "entry" }]);
  const [databasePayload, setDatabasePayload] = useState({});
  const [tradeData, setTradeData] = useState({
    entry: { overview: [], projection: [], outcome: [] },
    exit: { overview: [], projection: [], outcome: [] },
  });
  const [formData, setFormData] = useState({});
  const [transactionFields, setTransactionFields] = useState([]);
  const [tradeFields, setTradeFields] = useState([]);
  const [portfolioFields, setPortfolioFields] = useState([]);
  const [tradeId, setTradeId] = useState(null);
  const [tradePublishStatus, setTradePublishStatus] = useState("draft");
  const [formKeyMap, setFormKeyMap] = useState({ entry_1: null });
  const [hasPublishedEntry, setHasPublishedEntry] = useState(false);
  const hasInitialized = useRef(false);
  const [entryForms, setEntryForms] = useState([]);
  const [saveStatus, setSaveStatus] = useState(null);
  const router = useRouter();

  const addEntryForm = async () => {
    if (tradePublishStatus === "published") return;
    const count = tradeForms.filter((f) => f.type === "entry").length + 1;
    const newFormKey = `entry_${count}`;
    try {
      const res = await post("/trade/initialize", { type: "entry", index: count, trade_id: tradeId });
      if (res.success) {
        setFormKeyMap((prev) => ({ ...prev, [newFormKey]: res.form_key }));
        setTradeForms((prev) => [...prev, { id: prev.length + 1, type: "entry", index: count }]);
        setFormData((prev) => ({ ...prev, [res.form_key]: { ...tradeData.entry } }));
      } else {
        alert("Failed to add entry form.");
      }
    } catch (err) {
      console.error("Add Entry Form Error:", err.response?.data || err.message);
      alert("An error occurred while adding the entry form.");
    }
  };

  const addExitForm = async () => {
    if (tradePublishStatus === "published") return;
    const count = tradeForms.filter((f) => f.type === "exit").length + 1;
    const newFormKey = `exit_${count}`;
    try {
      const res = await post("/trade/initialize", { type: "exit", index: count, trade_id: tradeId });
      if (res.success) {
        setFormKeyMap((prev) => ({ ...prev, [newFormKey]: res.form_key }));
        setTradeForms((prev) => [...prev, { id: prev.length + 1, type: "exit", index: count }]);
        setFormData((prev) => ({ ...prev, [res.form_key]: { ...tradeData.exit } }));
      } else {
        alert("Failed to add exit form.");
      }
    } catch (err) {
      console.error("Add Exit Form Error:", err.response?.data || err.message);
      alert("An error occurred while adding the exit form.");
    }
  };

  const updateDatabasePayload = (formKey, updatedFields) => {
    setDatabasePayload(prev => ({
      ...prev,
      [formKey]: {
        ...prev[formKey],
        section: updatedFields.section,
        data: {
          ...prev[formKey]?.data,
          ...updatedFields.data,
        },
      },
    }));
  };

  const handlePublishTrade = async () => {
    if (tradePublishStatus === "published") return;
    try {
      for (const form of tradeForms) {
        const formNumber = tradeForms
          .filter((f, idx) => f.type === form.type && idx <= tradeForms.indexOf(form))
          .length;

        const formKey = `${form.type}_${formNumber}`;
        const numericFormKey = formKeyMap[formKey];

        if (numericFormKey) {
          const response = await get(`/trade/form-status/${numericFormKey}`);
          if (response.is_published) {
            setHasPublishedEntry(true);
            break;
          }
        }
      }

      if (!hasPublishedEntry) {
        alert("You must publish at least one form (Entry or Exit) before publishing the Trade.");
        return;
      }

      const res = await post("/trade/publish-trade", { tradeId });
      console.log("Publish trade response:", res);
      if (res.success) {
        setTradePublishStatus("published");
      }
    } catch (err) {
      console.error("Publish Trade Error:", err.response?.data || err.message);
      alert("Failed to publish trade.");
    }
  };

  const onDeleteForm = (deletedFormKey) => {
    setTradeForms((prev) =>
      prev.filter((form) => {
        const formKey = `${form.type}_${form.index}`;
        return formKeyMap[formKey] !== deletedFormKey;
      })
    );

    setFormData((prev) => {
      const newFormData = { ...prev };
      delete newFormData[deletedFormKey];
      return newFormData;
    });

    setFormKeyMap((prev) => {
      const newMap = { ...prev };
      const keyToDelete = Object.keys(prev).find((key) => prev[key] === deletedFormKey);
      if (keyToDelete) delete newMap[keyToDelete];
      return newMap;
    });
  };

  const onSaveStatusChange = (status) => {
    setSaveStatus(status);
    if (status === "success") {
      const timeoutId = setTimeout(() => {
        setSaveStatus(null);
      }, 2000);
      return () => clearTimeout(timeoutId);
    }
  };

  useEffect(() => {
    const init = async () => {
      if (hasInitialized.current) return;
      try {
        hasInitialized.current = true;

        let newTradeId, formKeyMap = {}, tradeForms = [];

        if (tradeIdParam) {
          const tradeRes = await get(`/trade/${tradeIdParam}/data`);
          if (!tradeRes.success) {
            router.push("/dashboard/trade-manager");
            return;
          }
          newTradeId = tradeRes.trade.trade_id;
          setTradeId(tradeRes.trade.trade_id);
          tradeRes.forms.forEach((form, idx) => {
            const formKey = `${form.type}_${form.index}`;
            formKeyMap[formKey] = form.form_key;
            tradeForms.push({ id: idx + 1, type: form.type, index: form.index });
          });
          setTradePublishStatus(tradeRes.trade.is_published ? "published" : "draft");
        } else {
            const initRes = await post("/trade/initialize", { type: "entry", index: 1, trade_id: newTradeId ? newTradeId : null  });
            if (!initRes.success) {
              throw new Error("Failed to initialize trade");
            }
            newTradeId = initRes.trade_id;
            setTradeId(initRes.trade_id);
            formKeyMap = { entry_1: initRes.form_key };
            tradeForms = [{ id: 1, type: "entry", index: 1 }];
        }

        setFormKeyMap(formKeyMap);
        setTradeForms(tradeForms);

        const tradeRes = await get("/trade");
        const transform = (list) =>
          Array.isArray(list)
            ? list.map((item) => ({
                title: item.field_name || "",
                tooltip: item.summary || "",
                input: item.database_field || "",
                account_field: item.account_field || "",
                portfolioValue: item.portfolioValue ?? "",
                is_editable: item.is_editable ?? true,
                datatype: item.datatype || "",
                metric_dimension: item.metric_dimension || "",
                expected_values: Array.isArray(item.expected_values) ? item.expected_values : [],
                has_formula: item.has_formula || false,
              }))
            : [];

        const entryOverview = transform(tradeRes.entry_overview);
        const entryProjection = transform(tradeRes.entry_projection);
        const entryOutcome = transform(tradeRes.entry_outcome);
        const exitOverview = transform(tradeRes.exit_overview);
        const exitProjection = transform(tradeRes.exit_projection);
        const exitOutcome = transform(tradeRes.exit_outcome);

        const newTradeData = {
          entry: { overview: entryOverview, projection: entryProjection, outcome: entryOutcome },
          exit: { overview: exitOverview, projection: exitProjection, outcome: exitOutcome },
        };

        let savedFormData = {};
        if (tradeIdParam) {
          const savedRes = await get(`/trade/${tradeIdParam}/data`);
          if (!savedRes.success) {
            router.push("/dashboard/trade-manager"); // Redirect to dashboard
            return;
          }
          if (savedRes.success) {
            savedRes.forms.forEach((form) => {
              const formType = form.type;
              const formKey = form.form_key;
              const formDataForKey = {
                overview: [...newTradeData[formType].overview],
                projection: [...newTradeData[formType].projection],
                outcome: [...newTradeData[formType].outcome],
              };
              const formulaModeInputs = {};
              form.sections.forEach((section) => {
                const sectionName = section.section;
                if (section.data) {
                  Object.values(section.data).forEach(({ input, value }) => {
                    const fieldIndex = formDataForKey[sectionName].findIndex((field) => field.input === input);
                    if (fieldIndex !== -1) {
                      formDataForKey[sectionName][fieldIndex] = {
                        ...formDataForKey[sectionName][fieldIndex],
                        portfolioValue: value,
                      };
                    } else if (input === "linked_entry" && formType === "exit" && sectionName === "overview") {
                      formDataForKey.overview.push({ input: "linked_entry", portfolioValue: value, is_editable: true });
                    } else if (input === "transaction_comments") {
                      formDataForKey.overview.push({ input: "transaction_comments", portfolioValue: value, is_editable: true });
                    }
                  });
                  if (section.formula_data) {
                    formDataForKey.formulaModeInputs = section.formula_data;
                }
              }
              });
              savedFormData[formKey] = formDataForKey;
            });
          }
        } else {
          savedFormData = Object.keys(formKeyMap).reduce((acc, formKey) => {
            const formType = formKey.split("_")[0];
            acc[formKeyMap[formKey]] = {
              overview:
                formType === "entry" && newTradeData.entry.overview.length
                  ? newTradeData.entry.overview
                  : [{ input: "transaction_risk_percentage", title: "Risk", portfolioValue: "" }],
              projection: newTradeData[formType].projection,
              outcome: newTradeData[formType].outcome,
            };
            return acc;
          }, {});
        }

        setTradeData(newTradeData);
        setFormData(savedFormData);
      } catch (err) {
          console.error("Initialize Trade Error:", err.response?.data || err.message);
          alert("An error occurred while initializing the trade.");
      }
    };

    init();
  }, [tradeIdParam, router]);

  useEffect(() => {
    const entryForms = tradeForms
      .filter((form) => form.type === "entry")
      .map((form) => ({ formKey: formKeyMap[`${form.type}_${form.index}`], index: form.index }));
    setEntryForms(entryForms);
  }, [tradeForms, formKeyMap]);

  useEffect(() => {
    const fetchFields = async () => {
      try {
        const res = await get("/trade/fetch/fields");
        setTransactionFields(res.transactions || []);
        setTradeFields(res.trades || []);
        setPortfolioFields(res.portfolios || []);
      } catch (err) {
        console.error("Fetch Fields Error:", err.response?.data || err.message);
      }
    };
    fetchFields();
  }, []);

  const entrydata = [
    {
      tarde: "EXIT 2",
      entrydate: "Entry Date",
      ticker: "Ticker",
      entryprice: "Entry Price",
      exitprice: "Exit Price",
      position: "Position",
      pl: "P&L$",
    },
    {
      tarde: "EXIT 1",
      entrydate: "Entry Date",
      ticker: "Ticker",
      entryprice: "Entry Price",
      exitprice: "Exit Price",
      position: "Position",
      pl: "P&L$",
    },
  ];

  const metaArray = {
    noindex: true,
    title: "Trade Builder | Manually Build Your Trades | TradeReply",
    description: "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    canonical_link: "https://www.tradereply.com/dashboard/trade-builder",
    og_site_name: "TradeReply",
    og_title: "Trade Builder | Manually Build Your Trades | TradeReply",
    og_description: "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    twitter_title: "Trade Builder | Manually Build Your Trades | TradeReply",
    twitter_description: "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
  };

  return (
    <DashboardLayout>
      <MetaHead props={metaArray} />
      <div className="trade_manager trade_builder">
        <CommonHead 
            isShowCalender="false" 
            isDefault={saveStatus === "default"}
            isSaving={saveStatus === "loading"}
            isSuccess={saveStatus === "success"}
            isError={saveStatus === "error"}
        />
        <Container>
          <Row className="trade_head align-items-center pt-4 pb-6">
            <Col md={4}><AdminHeading heading="Trade Builder" centered /></Col>
            <Col md={4}><div className="trade_head_title"><h4>{tradePublishStatus === "published" ? "Published" : "Draft 3"}</h4></div></Col>
            <Col md={4}>
              <div className="trade_head_btns d-sm-flex">
                <CommonButton title="Delete" className="red-btn me-2"/>
                <CommonButton title="Publish" className="green-btn" onClick={handlePublishTrade} disabled={!hasPublishedEntry || tradePublishStatus === "published"} />
              </div>
            </Col>
          </Row>

          {tradeForms.map((form, index) => {
            const formKey = `${form.type}_${form.index}`;
            const numericFormKey = formKeyMap[formKey] || formKey;

            return (
              <div key={form.id} className={`trade_builder_card mb-4 ${form.type === "exit" ? "greengrandientbg" : ""}`}>
                {form.type === "entry" ? (
                  <TradeBuilderEntry
                    formKey={numericFormKey}
                    formData={formData}
                    setFormData={setFormData}
                    transactionFields={transactionFields}
                    tradeFields={tradeFields}
                    portfolioFields={portfolioFields}
                    updateDatabasePayload={updateDatabasePayload}
                    tradeId={tradeId}
                    tradePublishStatus={tradePublishStatus}
                    onDeleteForm={onDeleteForm}
                    onSaveStatusChange={onSaveStatusChange}
                  />
                ) : (
                  <TradeBuilderExit
                    formKey={numericFormKey}
                    formData={formData}
                    index={form.index}
                    setFormData={setFormData}
                    transactionFields={transactionFields}
                    tradeFields={tradeFields}
                    portfolioFields={portfolioFields}
                    updateDatabasePayload={updateDatabasePayload}
                    tradeId={tradeId}
                    tradePublishStatus={tradePublishStatus}
                    onDeleteForm={onDeleteForm}
                    entryForms={entryForms}
                    onSaveStatusChange={onSaveStatusChange}
                  />
                )}
              </div>
            );
          })}

          <div className="trade_manager_btns my-30">
            <Row>
              <Col lg={6}><CommonButton title="Add Entry" onlyIcon={<PlusIcon />} className="w-100" onClick={addEntryForm} disabled={tradePublishStatus === "published"} /></Col>
              <Col lg={6}><CommonButton title="Add Exit" onlyIcon={<PlusIcon />} className="w-100" onClick={addExitForm} disabled={tradePublishStatus === "published"} /></Col>
            </Row>
          </div>

          <div className="trade_manager_trade_entry mt-30">
            {entrydata.map((item, index) => (
              <div key={index} className={`trade_manager_trade_entry_box Redgrandient ${index === 1 ? "greengrandient" : ""}`}>
                <span className="solidArrow red_arrow me-3"><SolidRedArrowIcon /></span>
                <div className="d-flex trade_manager_trade_entry_box_headtext align-items-center w-100 justify-content-between">
                  <h5>{item.tarde}</h5>
                  <h5>{item.entrydate}</h5>
                  <h5>{item.ticker}</h5>
                  <h5>{item.entryprice}</h5>
                  <h5>{item.exitprice}</h5>
                  <h5>{item.position}</h5>
                  <h5>{item.pl}</h5>
                </div>
                <span className="solidArrow red_arrow endArrow ms-3"><SolidRedArrowIcon /></span>
              </div>
            ))}
          </div>
        </Container>
      </div>
    </DashboardLayout>
  );
};

const TradeBuilderPage = () => {
  return (
    <Suspense fallback={<TradeBuilderLoading />}>
      <TradeBuilder />
    </Suspense>
  );
};

export default TradeBuilderPage;
