<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Storage;

class NoProfanity implements Rule
{
    protected array $bannedWords;

    public function __construct()
    {
        $json = null;

        $storagePath = storage_path('app/tradereply-profanity.json');

        if (file_exists($storagePath)) {
            $json = json_decode(file_get_contents($storagePath), true);
        }
        elseif (file_exists(public_path('tradereply-profanity.json'))) {
            $json = json_decode(file_get_contents(public_path('tradereply-profanity.json')), true);
        }

        $this->bannedWords = $json['profanityWords'] ?? [];
        $this->bannedWords = array_filter($this->bannedWords, fn($word) => is_string($word));
    }




    public function passes($attribute, $value): bool
    {
        $normalized = $this->normalize($value);
        $words = preg_split('/[^a-z]+/', $normalized);

        \Log::debug('Normalized username: ' . $normalized);

        foreach ($this->bannedWords as $badWord) {
            if (!is_string($badWord)) {
                continue;
            }

            $badWord = strtolower($badWord);

            if (strlen($badWord) < 4 && in_array($badWord, $words)) {
                \Log::debug("Exact match found: $badWord");
                return false;
            }

            if (strlen($badWord) >= 4 && str_contains($normalized, $badWord)) {
                \Log::debug("Substring match found: $badWord");
                return false;
            }
        }

        return true;
    }




    public function message(): string
    {
        return 'The :attribute contains inappropriate or offensive language.';
    }

    /**
     * Normalize a username:
     * - Convert leetspeak to letters
     * - Remove all non-letter characters (digits, underscores, symbols)
     * - Convert to lowercase
     */
    protected function normalize(string $input): string
    {
        $leetMap = [
            '0' => 'o',
            '1' => 'i',
            '3' => 'e',
            '4' => 'a',
            '5' => 's',
            '6' => 'g',
            '7' => 't',
            '8' => 'b',
            '9' => 'g',
            '@' => 'a',
            '$' => 's',
            '+' => 't',
            '!' => 'i',
            '*' => '',
            '#' => 'h',
            '%' => '',
            '^' => '',
            '&' => 'and',
        ];

        $input = strtolower($input);
        $input = strtr($input, $leetMap);
        $input = preg_replace('/[^a-z]/', '', $input);

        return $input;
    }

}
