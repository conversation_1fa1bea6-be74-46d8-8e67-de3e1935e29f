<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidUsername implements Rule
{

    // protected $reserved = [
    //    'admin', 'support', 'help', 'info', 'root',
    //    'system', 'test', 'username', 'null', 'user', 'guest',
    //    'dashboard'
    // ];


    // protected string $failReason = '';

    public function passes($attribute, $value): bool
    {
        // $value = strtolower($value);

        // if (in_array($value, $this->reserved)) {
        //     $this->failReason = 'This username is reserved and cannot be used.';
        //     return false;
        // }

        // if (!preg_match('/^[a-z0-9_]+$/', $value)) {
        //     return false;
        // }

        // if (substr_count($value, '_') > 2) {
        //     return false;
        // }

        // if (str_contains($value, '__')) {
        //     return false;
        // }

        // if (str_starts_with($value, '_')) {
        //     return false;
        // }

        // if (preg_match('/^\d+$/', $value)) {
        //     return false;
        // }

        // if (preg_match('/(\d)\1{2,}/', $value)) {
        //     return false;
        // }

        // if (preg_match('/123456|234567|345678|456789|987654|876543/', $value)) {
        //     return false;
        // }

        // if (strlen($value) > 20) {
        //     return false;
        // }

        return true;
    }


    public function message(): string
    {
        return 'Usernames must be 3–20 characters, include at least one letter, and may only contain letters, numbers, and underscores (no consecutive underscores).';
    }
}
